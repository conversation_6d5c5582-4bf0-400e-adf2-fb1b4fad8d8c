<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>衍生收入计算测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        input {
            width: 120px;
            padding: 5px;
            margin-left: 10px;
        }
        .result {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f8ff;
            border-left: 4px solid #007acc;
            font-weight: bold;
        }
        .error {
            background-color: #ffe6e6;
            border-left-color: #cc0000;
            color: #cc0000;
        }
    </style>
</head>
<body>
    <h1>衍生收入计算测试</h1>
    
    <div class="test-section">
        <h2>JavaScript 浮点数计算测试</h2>
        
        <div class="input-group">
            <label>公证费:</label>
            <input type="number" id="notaryFee" value="99.99" step="0.01">
        </div>
        
        <div class="input-group">
            <label>畅行无忧收入:</label>
            <input type="number" id="carefreeIncome" value="99.99" step="0.01">
        </div>
        
        <div class="input-group">
            <label>延保收入:</label>
            <input type="number" id="extendedWarrantyIncome" value="99.99" step="0.01">
        </div>
        
        <div class="input-group">
            <label>VPS收入:</label>
            <input type="number" id="vpsIncome" value="99.99" step="0.01">
        </div>
        
        <div class="input-group">
            <label>前置利息:</label>
            <input type="number" id="preInterest" value="99.99" step="0.01">
        </div>
        
        <div class="input-group">
            <label>挂牌费:</label>
            <input type="number" id="licensePlateFee" value="99.99" step="0.01">
        </div>
        
        <div class="input-group">
            <label>临牌费:</label>
            <input type="number" id="tempPlateFee" value="99.99" step="0.01">
        </div>
        
        <div class="input-group">
            <label>外卖装具收入:</label>
            <input type="number" id="deliveryEquipment" value="99.99" step="0.01">
        </div>
        
        <div class="input-group">
            <label>其他收入:</label>
            <input type="number" id="otherIncome" value="99.99" step="0.01">
        </div>
        
        <button onclick="calculateTotal()">计算总收入</button>
        
        <div id="result" class="result">
            总收入将在这里显示
        </div>
    </div>

    <script>
        function calculateTotal() {
            // 获取所有输入值
            const notaryFee = parseFloat(document.getElementById('notaryFee').value) || 0;
            const carefreeIncome = parseFloat(document.getElementById('carefreeIncome').value) || 0;
            const extendedWarrantyIncome = parseFloat(document.getElementById('extendedWarrantyIncome').value) || 0;
            const vpsIncome = parseFloat(document.getElementById('vpsIncome').value) || 0;
            const preInterest = parseFloat(document.getElementById('preInterest').value) || 0;
            const licensePlateFee = parseFloat(document.getElementById('licensePlateFee').value) || 0;
            const tempPlateFee = parseFloat(document.getElementById('tempPlateFee').value) || 0;
            const deliveryEquipment = parseFloat(document.getElementById('deliveryEquipment').value) || 0;
            const otherIncome = parseFloat(document.getElementById('otherIncome').value) || 0;
            
            // 直接相加（可能出现浮点数精度问题）
            const directSum = notaryFee + carefreeIncome + extendedWarrantyIncome + vpsIncome + 
                            preInterest + licensePlateFee + tempPlateFee + deliveryEquipment + otherIncome;
            
            // 使用精确计算方法
            const preciseSum = calculatePreciseSum([
                notaryFee, carefreeIncome, extendedWarrantyIncome, vpsIncome,
                preInterest, licensePlateFee, tempPlateFee, deliveryEquipment, otherIncome
            ]);
            
            // 显示结果
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div>直接相加结果: ${directSum}</div>
                <div>精确计算结果: ${preciseSum}</div>
                <div>是否有精度问题: ${directSum !== preciseSum ? '是' : '否'}</div>
                <div>期望结果: ${9 * 99.99} (9个99.99相加)</div>
            `;
            
            // 如果有精度问题，标记为错误
            if (directSum !== preciseSum) {
                resultDiv.classList.add('error');
            } else {
                resultDiv.classList.remove('error');
            }
        }
        
        // 精确的浮点数加法函数
        function calculatePreciseSum(numbers) {
            // 将所有数字转换为整数进行计算，避免浮点数精度问题
            let sum = 0;
            const precision = 100; // 保留2位小数，所以乘以100
            
            for (let num of numbers) {
                sum += Math.round(num * precision);
            }
            
            return sum / precision;
        }
        
        // 页面加载时自动计算一次
        window.onload = function() {
            calculateTotal();
        };
        
        // 为所有输入框添加事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input[type="number"]');
            inputs.forEach(input => {
                input.addEventListener('input', calculateTotal);
            });
        });
    </script>
</body>
</html>
