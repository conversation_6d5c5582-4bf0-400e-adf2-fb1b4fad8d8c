/**
 * 测试浮点数精度修复
 */

// 原始的直接相加方法（有精度问题）
function directSum(numbers) {
  return numbers.reduce((sum, num) => sum + (num || 0), 0);
}

// 修复后的精确计算方法
function calculatePreciseSum(numbers) {
  // 将所有数字转换为整数进行计算，避免浮点数精度问题
  let sum = 0;
  const precision = 100; // 保留2位小数，所以乘以100
  
  for (let num of numbers) {
    if (num && typeof num === 'number') {
      sum += Math.round(num * precision);
    }
  }
  
  return sum / precision;
}

// 测试数据：9个99.99
const testData = [99.99, 99.99, 99.99, 99.99, 99.99, 99.99, 99.99, 99.99, 99.99];

console.log('=== 浮点数精度测试 ===');
console.log('测试数据:', testData);
console.log('期望结果:', 9 * 99.99);

const directResult = directSum(testData);
const preciseResult = calculatePreciseSum(testData);

console.log('\n=== 计算结果 ===');
console.log('直接相加结果:', directResult);
console.log('精确计算结果:', preciseResult);
console.log('期望结果:', 899.91);

console.log('\n=== 精度对比 ===');
console.log('直接相加是否准确:', directResult === 899.91);
console.log('精确计算是否准确:', preciseResult === 899.91);
console.log('直接相加误差:', Math.abs(directResult - 899.91));
console.log('精确计算误差:', Math.abs(preciseResult - 899.91));

// 测试其他边界情况
console.log('\n=== 边界情况测试 ===');

// 测试包含0和undefined的情况
const testDataWithNulls = [99.99, 0, 99.99, undefined, 99.99, null, 99.99];
console.log('包含null/undefined的数据:', testDataWithNulls);
console.log('精确计算结果:', calculatePreciseSum(testDataWithNulls));
console.log('期望结果:', 4 * 99.99, '=', 399.96);

// 测试小数点精度
const precisionTest = [0.1, 0.2, 0.3];
console.log('\n小数精度测试:', precisionTest);
console.log('直接相加:', directSum(precisionTest));
console.log('精确计算:', calculatePreciseSum(precisionTest));
console.log('期望结果:', 0.6);
